#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
移动端优化功能测试脚本
测试移动端相关的CSS和JavaScript文件是否正确加载和工作
"""

import os
import sys
import requests
from pathlib import Path

def test_file_exists(file_path, description):
    """测试文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} - 文件不存在")
        return False

def test_css_content(file_path, expected_content, description):
    """测试CSS文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if expected_content in content:
                print(f"✅ {description}: 包含预期内容")
                return True
            else:
                print(f"❌ {description}: 缺少预期内容")
                return False
    except Exception as e:
        print(f"❌ {description}: 读取文件失败 - {str(e)}")
        return False

def test_js_content(file_path, expected_content, description):
    """测试JavaScript文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if expected_content in content:
                print(f"✅ {description}: 包含预期内容")
                return True
            else:
                print(f"❌ {description}: 缺少预期内容")
                return False
    except Exception as e:
        print(f"❌ {description}: 读取文件失败 - {str(e)}")
        return False

def test_template_integration(file_path, expected_content, description):
    """测试模板文件集成"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if expected_content in content:
                print(f"✅ {description}: 已正确集成")
                return True
            else:
                print(f"❌ {description}: 未正确集成")
                return False
    except Exception as e:
        print(f"❌ {description}: 读取文件失败 - {str(e)}")
        return False

def test_route_exists():
    """测试移动端演示路由是否存在"""
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if '/mobile-demo' in content and 'mobile_demo' in content:
                print("✅ 移动端演示路由: 已正确添加")
                return True
            else:
                print("❌ 移动端演示路由: 未找到")
                return False
    except Exception as e:
        print(f"❌ 移动端演示路由: 检查失败 - {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔍 开始测试移动端优化功能...")
    print("=" * 50)
    
    test_results = []
    
    # 测试CSS文件
    print("\n📄 测试CSS文件:")
    test_results.append(test_file_exists(
        'app/static/css/mobile-optimization.css',
        '移动端优化CSS文件'
    ))
    
    test_results.append(test_css_content(
        'app/static/css/mobile-optimization.css',
        '@media (max-width: 768px)',
        '移动端媒体查询'
    ))
    
    test_results.append(test_css_content(
        'app/static/css/mobile-optimization.css',
        'min-height: 44px',
        '触控目标优化'
    ))
    
    # 测试JavaScript文件
    print("\n📜 测试JavaScript文件:")
    test_results.append(test_file_exists(
        'app/static/js/mobile-enhancements.js',
        '移动端增强JS文件'
    ))
    
    test_results.append(test_js_content(
        'app/static/js/mobile-enhancements.js',
        'class MobileEnhancements',
        '移动端增强类'
    ))
    
    test_results.append(test_js_content(
        'app/static/js/mobile-enhancements.js',
        'initTouchEnhancements',
        '触控增强功能'
    ))
    
    # 测试模板集成
    print("\n🌐 测试模板集成:")
    test_results.append(test_template_integration(
        'app/templates/base.html',
        'mobile-optimization.css',
        '基础模板CSS集成'
    ))
    
    test_results.append(test_template_integration(
        'app/templates/base.html',
        'mobile-enhancements.js',
        '基础模板JS集成'
    ))
    
    test_results.append(test_file_exists(
        'app/templates/mobile-demo.html',
        '移动端演示页面模板'
    ))
    
    # 测试路由
    print("\n🛣️ 测试路由:")
    test_results.append(test_route_exists())
    
    # 测试文档
    print("\n📚 测试文档:")
    test_results.append(test_file_exists(
        'MOBILE_OPTIMIZATION_README.md',
        '移动端优化说明文档'
    ))
    
    # 统计结果
    print("\n" + "=" * 50)
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"📊 测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！移动端优化功能已正确实现。")
        print("\n📱 使用说明:")
        print("1. 启动应用: python run.py")
        print("2. 访问演示页面: http://localhost:5000/mobile-demo")
        print("3. 使用移动设备或调整浏览器窗口大小至768px以下查看效果")
        return True
    else:
        print("⚠️ 部分测试未通过，请检查相关文件。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
