#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查优雅菜单中的路由有效性
确保所有菜单项都指向有效的路由端点
"""

import os
import sys

def check_menu_routes():
    """检查菜单路由有效性"""
    try:
        # 导入应用和菜单配置
        from app import create_app
        from app.utils.menu_elegant import ELEGANT_MENU_CONFIG
        
        app = create_app()
        
        print("🔍 检查优雅菜单路由有效性...")
        print("=" * 60)
        
        # 获取所有可用的路由端点
        available_endpoints = set()
        for rule in app.url_map.iter_rules():
            available_endpoints.add(rule.endpoint)
        
        print(f"📋 系统中共有 {len(available_endpoints)} 个路由端点")
        
        # 检查菜单中的路由
        invalid_routes = []
        valid_routes = []
        
        def check_menu_item(item, level=0):
            """递归检查菜单项"""
            indent = "  " * level
            
            if 'url' in item:
                endpoint = item['url']
                if endpoint in available_endpoints:
                    print(f"{indent}✅ {item['name']}: {endpoint}")
                    valid_routes.append(endpoint)
                else:
                    print(f"{indent}❌ {item['name']}: {endpoint} - 路由不存在")
                    invalid_routes.append({
                        'name': item['name'],
                        'endpoint': endpoint,
                        'id': item.get('id', 'unknown')
                    })
            
            # 检查子菜单
            if 'children' in item:
                for child in item['children']:
                    if not child.get('is_header'):  # 跳过分组标题
                        check_menu_item(child, level + 1)
        
        # 检查所有菜单项
        for menu_item in ELEGANT_MENU_CONFIG:
            print(f"\n📂 检查菜单: {menu_item['name']}")
            check_menu_item(menu_item)
        
        # 总结结果
        print("\n" + "=" * 60)
        print(f"📊 检查结果:")
        print(f"  有效路由: {len(valid_routes)}个")
        print(f"  无效路由: {len(invalid_routes)}个")
        
        if invalid_routes:
            print(f"\n❌ 发现无效路由:")
            for route in invalid_routes:
                print(f"  - {route['name']} ({route['id']}): {route['endpoint']}")
                
            print(f"\n🔧 建议的修复方案:")
            for route in invalid_routes:
                endpoint = route['endpoint']
                suggestions = []
                
                # 查找相似的端点
                for available in available_endpoints:
                    if any(word in available.lower() for word in endpoint.lower().split('.')):
                        suggestions.append(available)
                
                if suggestions:
                    print(f"  {route['name']} ({endpoint}):")
                    for suggestion in suggestions[:3]:  # 只显示前3个建议
                        print(f"    建议: {suggestion}")
                else:
                    print(f"  {route['name']} ({endpoint}): 未找到相似路由")
            
            return False
        else:
            print("🎉 所有菜单路由都有效！")
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def suggest_route_fixes():
    """建议路由修复方案"""
    print("\n🔧 常见路由修复建议:")
    print("-" * 60)
    
    fixes = {
        'main.quick_actions': [
            'daily_management.index',
            'main.index',
            'inspection.quick_pass'
        ],
        'food_safety.index': [
            'food_sample.index',
            'food_trace.index',
            'daily_management.index'
        ],
        'admin_data.data_management': [
            'system.dashboard',
            'system.settings',
            'data_repair.index'
        ]
    }
    
    for invalid, suggestions in fixes.items():
        print(f"如果 {invalid} 无效，可以考虑:")
        for suggestion in suggestions:
            print(f"  - {suggestion}")
        print()

def main():
    """主函数"""
    print("🔍 开始检查优雅菜单路由有效性...")
    
    success = check_menu_routes()
    
    if not success:
        suggest_route_fixes()
        print("\n⚠️ 请修复无效路由后重新测试。")
    else:
        print("\n🚀 所有路由检查通过，菜单可以正常使用！")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
