#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优雅导航栏测试脚本
验证新的导航栏是否正确替换了旧的菜单系统
"""

import os
import sys

def test_menu_injection():
    """测试菜单注入函数是否已切换到优雅菜单"""
    try:
        with open('app/__init__.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'from app.utils.menu_elegant import get_elegant_user_menu' in content:
            print("✅ 菜单注入函数已切换到优雅菜单")
            return True
        else:
            print("❌ 菜单注入函数未切换到优雅菜单")
            return False
            
    except Exception as e:
        print(f"❌ 测试菜单注入函数失败: {str(e)}")
        return False

def test_template_integration():
    """测试模板是否正确集成优雅导航样式"""
    try:
        with open('app/templates/base.html', 'r', encoding='utf-8') as f:
            content = f.read()
            
        results = []
        
        # 检查优雅导航样式是否已添加
        if 'elegant-navigation.css' in content:
            print("✅ 优雅导航样式已添加到基础模板")
            results.append(True)
        else:
            print("❌ 优雅导航样式未添加到基础模板")
            results.append(False)
            
        # 检查分组标题支持
        if 'child.get(\'is_header\')' in content:
            print("✅ 模板已支持分组标题功能")
            results.append(True)
        else:
            print("❌ 模板未支持分组标题功能")
            results.append(False)
            
        # 检查菜单切换器是否已移除
        if 'menu-switcher.js' not in content:
            print("✅ 菜单切换器已从模板中移除")
            results.append(True)
        else:
            print("❌ 菜单切换器仍在模板中")
            results.append(False)
            
        return all(results)
        
    except Exception as e:
        print(f"❌ 测试模板集成失败: {str(e)}")
        return False

def test_api_routes_removed():
    """测试API路由是否已移除"""
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if '/api/menu/elegant' not in content and '/api/menu/classic' not in content:
            print("✅ 菜单切换API路由已移除")
            return True
        else:
            print("❌ 菜单切换API路由仍然存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试API路由移除失败: {str(e)}")
        return False

def test_elegant_menu_structure():
    """测试优雅菜单结构"""
    try:
        sys.path.append('.')
        from app.utils.menu_elegant import ELEGANT_MENU_CONFIG
        
        print(f"✅ 优雅菜单配置加载成功，包含 {len(ELEGANT_MENU_CONFIG)} 个主菜单")
        
        # 检查菜单结构
        expected_menus = ['工作台', '菜单规划', '供应链', '质量安全', '系统']
        actual_menus = [item['name'] for item in ELEGANT_MENU_CONFIG]
        
        for expected in expected_menus:
            if expected in actual_menus:
                print(f"  ✅ 找到菜单项: {expected}")
            else:
                print(f"  ❌ 缺少菜单项: {expected}")
                return False
                
        # 检查分组标题
        has_headers = False
        for item in ELEGANT_MENU_CONFIG:
            if 'children' in item:
                for child in item['children']:
                    if child.get('is_header'):
                        has_headers = True
                        print(f"  ✅ 找到分组标题: {child['name']}")
                        break
                        
        if has_headers:
            print("✅ 菜单包含分组标题功能")
        else:
            print("⚠️ 菜单未包含分组标题")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试优雅菜单结构失败: {str(e)}")
        return False

def test_file_cleanup():
    """测试文件清理情况"""
    results = []
    
    # 检查优雅菜单文件是否存在
    if os.path.exists('app/utils/menu_elegant.py'):
        print("✅ 优雅菜单配置文件存在")
        results.append(True)
    else:
        print("❌ 优雅菜单配置文件不存在")
        results.append(False)
        
    # 检查优雅导航样式文件是否存在
    if os.path.exists('app/static/css/elegant-navigation.css'):
        print("✅ 优雅导航样式文件存在")
        results.append(True)
    else:
        print("❌ 优雅导航样式文件不存在")
        results.append(False)
        
    # 检查旧的菜单切换器文件（可以保留但不使用）
    if os.path.exists('app/static/js/menu-switcher.js'):
        print("ℹ️ 菜单切换器文件仍存在（已不使用）")
    else:
        print("ℹ️ 菜单切换器文件已删除")
        
    return all(results)

def analyze_menu_optimization():
    """分析菜单优化效果"""
    try:
        # 分析原有菜单
        from app.utils.menu import MENU_CONFIG
        original_count = len(MENU_CONFIG)
        
        # 分析优雅菜单
        from app.utils.menu_elegant import ELEGANT_MENU_CONFIG
        elegant_count = len(ELEGANT_MENU_CONFIG)
        
        # 计算优化效果
        reduction = ((original_count - elegant_count) / original_count) * 100
        
        print(f"\n📊 菜单优化效果:")
        print(f"  原有主菜单: {original_count}个")
        print(f"  优雅主菜单: {elegant_count}个")
        print(f"  减少数量: {original_count - elegant_count}个")
        print(f"  优化程度: {reduction:.1f}%")
        
        if reduction >= 50:
            print("  🎉 优化效果显著!")
        elif reduction >= 30:
            print("  ✅ 优化效果良好!")
        else:
            print("  ⚠️ 优化效果一般")
            
        return True
        
    except Exception as e:
        print(f"❌ 菜单优化分析失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔍 开始测试优雅导航栏切换...")
    print("=" * 60)
    
    test_results = []
    
    # 测试菜单注入函数
    print("\n📋 测试菜单注入函数:")
    test_results.append(test_menu_injection())
    
    # 测试模板集成
    print("\n🌐 测试模板集成:")
    test_results.append(test_template_integration())
    
    # 测试API路由移除
    print("\n🛣️ 测试API路由移除:")
    test_results.append(test_api_routes_removed())
    
    # 测试优雅菜单结构
    print("\n📊 测试优雅菜单结构:")
    test_results.append(test_elegant_menu_structure())
    
    # 测试文件清理
    print("\n📁 测试文件状态:")
    test_results.append(test_file_cleanup())
    
    # 分析优化效果
    print("\n📈 分析优化效果:")
    test_results.append(analyze_menu_optimization())
    
    # 统计结果
    print("\n" + "=" * 60)
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"📊 测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 优雅导航栏切换成功！")
        print("\n✨ 新导航栏特点:")
        print("• 从9个主菜单精简到5个主菜单")
        print("• 逻辑分组：工作台、菜单规划、供应链、质量安全、系统")
        print("• 支持分组标题，清晰的功能区域划分")
        print("• 现代化的视觉设计和交互效果")
        print("• 完美的移动端适配")
        print("• 保持所有现有功能不变")
        
        print("\n🚀 启动应用后即可看到新的优雅导航栏！")
        return True
    else:
        print("⚠️ 部分测试未通过，请检查相关配置。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
