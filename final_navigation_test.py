#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终导航栏测试脚本
验证优雅导航栏是否完全正常工作
"""

import os
import sys
import requests
import time

def test_application_startup():
    """测试应用启动"""
    print("🚀 测试应用启动...")
    
    try:
        response = requests.get('http://localhost:5000', timeout=10)
        if response.status_code == 200:
            print("✅ 应用启动成功")
            return True
        else:
            print(f"❌ 应用响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 应用连接失败: {str(e)}")
        return False

def test_menu_configuration():
    """测试菜单配置"""
    print("\n📋 测试菜单配置...")
    
    try:
        from app.utils.menu_elegant import ELEGANT_MENU_CONFIG
        
        # 检查菜单结构
        expected_structure = {
            '工作台': 3,
            '菜单规划': 4,
            '供应链': 12,
            '质量安全': 4,
            '系统': 8
        }
        
        actual_structure = {}
        for item in ELEGANT_MENU_CONFIG:
            actual_structure[item['name']] = len(item.get('children', []))
        
        all_correct = True
        for name, expected_count in expected_structure.items():
            actual_count = actual_structure.get(name, 0)
            if actual_count == expected_count:
                print(f"  ✅ {name}: {actual_count}个子项")
            else:
                print(f"  ❌ {name}: 期望{expected_count}个子项，实际{actual_count}个")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 菜单配置测试失败: {str(e)}")
        return False

def test_route_validity():
    """测试路由有效性"""
    print("\n🛣️ 测试路由有效性...")
    
    try:
        from app import create_app
        from app.utils.menu_elegant import ELEGANT_MENU_CONFIG
        
        app = create_app()
        available_endpoints = {rule.endpoint for rule in app.url_map.iter_rules()}
        
        invalid_routes = []
        total_routes = 0
        
        def check_routes(item):
            nonlocal total_routes, invalid_routes
            if 'url' in item:
                total_routes += 1
                if item['url'] not in available_endpoints:
                    invalid_routes.append(item['url'])
            
            if 'children' in item:
                for child in item['children']:
                    if not child.get('is_header'):
                        check_routes(child)
        
        for menu_item in ELEGANT_MENU_CONFIG:
            check_routes(menu_item)
        
        if invalid_routes:
            print(f"❌ 发现{len(invalid_routes)}个无效路由:")
            for route in invalid_routes:
                print(f"  - {route}")
            return False
        else:
            print(f"✅ 所有{total_routes}个路由都有效")
            return True
            
    except Exception as e:
        print(f"❌ 路由有效性测试失败: {str(e)}")
        return False

def test_template_integration():
    """测试模板集成"""
    print("\n🌐 测试模板集成...")
    
    try:
        with open('app/templates/base.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('elegant-navigation.css', '优雅导航样式'),
            ('child.get(\'is_header\')', '分组标题支持'),
            ('dropdown-header', '分组标题样式')
        ]
        
        all_passed = True
        for check, description in checks:
            if check in content:
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ {description}")
                all_passed = False
        
        # 检查菜单切换器是否已移除
        if 'menu-switcher.js' not in content:
            print("  ✅ 菜单切换器已移除")
        else:
            print("  ❌ 菜单切换器仍然存在")
            all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 模板集成测试失败: {str(e)}")
        return False

def test_menu_injection():
    """测试菜单注入"""
    print("\n💉 测试菜单注入...")
    
    try:
        with open('app/__init__.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'get_elegant_user_menu' in content:
            print("  ✅ 菜单注入函数已切换到优雅菜单")
            return True
        else:
            print("  ❌ 菜单注入函数未切换")
            return False
            
    except Exception as e:
        print(f"❌ 菜单注入测试失败: {str(e)}")
        return False

def test_visual_elements():
    """测试视觉元素"""
    print("\n🎨 测试视觉元素...")
    
    try:
        with open('app/static/css/elegant-navigation.css', 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        visual_features = [
            ('backdrop-filter', '毛玻璃效果'),
            ('border-radius', '圆角设计'),
            ('transition', '动画过渡'),
            ('@media (max-width: 768px)', '移动端适配'),
            ('dropdown-header', '分组标题样式')
        ]
        
        all_present = True
        for feature, description in visual_features:
            if feature in css_content:
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ {description}")
                all_present = False
        
        return all_present
        
    except Exception as e:
        print(f"❌ 视觉元素测试失败: {str(e)}")
        return False

def calculate_optimization_metrics():
    """计算优化指标"""
    print("\n📊 计算优化指标...")
    
    try:
        from app.utils.menu import MENU_CONFIG
        from app.utils.menu_elegant import ELEGANT_MENU_CONFIG
        
        original_count = len(MENU_CONFIG)
        elegant_count = len(ELEGANT_MENU_CONFIG)
        
        reduction = ((original_count - elegant_count) / original_count) * 100
        
        print(f"  📈 原有主菜单: {original_count}个")
        print(f"  📉 优雅主菜单: {elegant_count}个")
        print(f"  📊 减少数量: {original_count - elegant_count}个")
        print(f"  🎯 优化程度: {reduction:.1f}%")
        
        if reduction >= 40:
            print("  🎉 优化效果显著!")
            return True
        else:
            print("  ⚠️ 优化效果一般")
            return False
            
    except Exception as e:
        print(f"❌ 优化指标计算失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔍 开始最终导航栏测试...")
    print("=" * 60)
    
    test_results = []
    
    # 测试应用启动
    test_results.append(test_application_startup())
    
    # 测试菜单配置
    test_results.append(test_menu_configuration())
    
    # 测试路由有效性
    test_results.append(test_route_validity())
    
    # 测试模板集成
    test_results.append(test_template_integration())
    
    # 测试菜单注入
    test_results.append(test_menu_injection())
    
    # 测试视觉元素
    test_results.append(test_visual_elements())
    
    # 计算优化指标
    test_results.append(calculate_optimization_metrics())
    
    # 统计结果
    print("\n" + "=" * 60)
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"📊 最终测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("\n🎉 优雅导航栏完美运行！")
        print("\n✨ 新导航栏特点:")
        print("• 🎯 从9个主菜单精简到5个主菜单")
        print("• 📋 逻辑分组：工作台、菜单规划、供应链、质量安全、系统")
        print("• 🏷️ 支持分组标题，清晰的功能区域划分")
        print("• 🎨 现代化的视觉设计和交互效果")
        print("• 📱 完美的移动端适配")
        print("• ✅ 保持所有现有功能不变")
        
        print("\n🚀 用户现在可以享受更优雅的导航体验！")
        print("🌐 访问 http://localhost:5000 查看效果")
        return True
    else:
        print(f"\n⚠️ {total - passed}项测试未通过，请检查相关配置。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
