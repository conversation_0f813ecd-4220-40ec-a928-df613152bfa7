/**
 * 移动端增强功能
 * 提供移动端专用的交互优化和用户体验改进
 */

class MobileEnhancements {
    constructor() {
        this.isMobile = window.innerWidth <= 768;
        this.isTouch = 'ontouchstart' in window;
        this.init();
    }

    init() {
        if (this.isMobile) {
            this.initMobileOptimizations();
            this.initTouchEnhancements();
            this.initMobileNavigation();
            this.initMobileTableOptimizations();
            this.initMobileFormEnhancements();
            this.initMobileModalOptimizations();
            this.bindEvents();
        }
    }

    /**
     * 初始化移动端基础优化
     */
    initMobileOptimizations() {
        // 防止iOS Safari缩放
        this.preventZoom();

        // 添加移动端CSS类
        document.body.classList.add('mobile-device');

        // 优化触控目标大小
        this.optimizeTouchTargets();

        // 添加移动端视口meta标签优化
        this.optimizeViewport();
    }

    /**
     * 防止iOS Safari意外缩放
     */
    preventZoom() {
        document.addEventListener('touchstart', function(event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        }, { passive: false });

        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    }

    /**
     * 优化触控目标大小
     */
    optimizeTouchTargets() {
        const minTouchSize = 44; // 44px 是推荐的最小触控目标大小

        const elements = document.querySelectorAll('.btn, .nav-link, .dropdown-item, a, button');
        elements.forEach(element => {
            const rect = element.getBoundingClientRect();
            if (rect.height < minTouchSize) {
                element.style.minHeight = minTouchSize + 'px';
                element.style.display = 'flex';
                element.style.alignItems = 'center';
            }
        });
    }

    /**
     * 优化视口设置
     */
    optimizeViewport() {
        let viewport = document.querySelector('meta[name="viewport"]');
        if (viewport) {
            viewport.setAttribute('content',
                'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
            );
        }
    }

    /**
     * 初始化触控增强功能
     */
    initTouchEnhancements() {
        // 添加触控反馈
        this.addTouchFeedback();

        // 长按功能
        this.initLongPress();

        // 滑动手势
        this.initSwipeGestures();
    }

    /**
     * 添加触控反馈
     */
    addTouchFeedback() {
        const touchElements = document.querySelectorAll('.btn, .nav-link, .dropdown-item, .card');

        touchElements.forEach(element => {
            element.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
                this.style.transition = 'transform 0.1s';
            }, { passive: true });

            element.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            }, { passive: true });

            element.addEventListener('touchcancel', function() {
                this.style.transform = 'scale(1)';
            }, { passive: true });
        });
    }

    /**
     * 初始化长按功能
     */
    initLongPress() {
        let pressTimer;

        document.addEventListener('touchstart', function(e) {
            const target = e.target.closest('[data-long-press]');
            if (target) {
                pressTimer = setTimeout(() => {
                    const event = new CustomEvent('longpress', {
                        detail: { target: target }
                    });
                    target.dispatchEvent(event);

                    // 触觉反馈（如果支持）
                    if (navigator.vibrate) {
                        navigator.vibrate(50);
                    }
                }, 500);
            }
        }, { passive: true });

        document.addEventListener('touchend', function() {
            clearTimeout(pressTimer);
        }, { passive: true });

        document.addEventListener('touchmove', function() {
            clearTimeout(pressTimer);
        }, { passive: true });
    }

    /**
     * 初始化滑动手势
     */
    initSwipeGestures() {
        let startX, startY, startTime;

        document.addEventListener('touchstart', function(e) {
            const touch = e.touches[0];
            startX = touch.clientX;
            startY = touch.clientY;
            startTime = Date.now();
        }, { passive: true });

        document.addEventListener('touchend', function(e) {
            if (!startX || !startY) return;

            const touch = e.changedTouches[0];
            const endX = touch.clientX;
            const endY = touch.clientY;
            const endTime = Date.now();

            const deltaX = endX - startX;
            const deltaY = endY - startY;
            const deltaTime = endTime - startTime;

            // 检测快速滑动
            if (deltaTime < 300 && Math.abs(deltaX) > 50) {
                const direction = deltaX > 0 ? 'right' : 'left';
                const target = e.target.closest('[data-swipe]');

                if (target) {
                    const event = new CustomEvent('swipe', {
                        detail: { direction: direction, target: target }
                    });
                    target.dispatchEvent(event);
                }
            }

            startX = startY = null;
        }, { passive: true });
    }

    /**
     * 初始化移动端导航优化
     */
    initMobileNavigation() {
        // 优化下拉菜单
        this.optimizeDropdowns();

        // 添加返回顶部按钮
        this.addBackToTop();

        // 优化导航栏收缩
        this.optimizeNavbarCollapse();
    }

    /**
     * 优化下拉菜单
     */
    optimizeDropdowns() {
        const dropdowns = document.querySelectorAll('.dropdown');

        dropdowns.forEach(dropdown => {
            const toggle = dropdown.querySelector('.dropdown-toggle');
            const menu = dropdown.querySelector('.dropdown-menu');

            if (toggle && menu) {
                // 移动端点击行为优化
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // 关闭其他下拉菜单
                    document.querySelectorAll('.dropdown-menu.show').forEach(otherMenu => {
                        if (otherMenu !== menu) {
                            otherMenu.classList.remove('show');
                        }
                    });

                    menu.classList.toggle('show');
                });
            }
        });

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });
    }

    /**
     * 添加返回顶部按钮
     */
    addBackToTop() {
        const backToTop = document.createElement('button');
        backToTop.innerHTML = '<i class="fas fa-chevron-up"></i>';
        backToTop.className = 'btn btn-primary back-to-top-mobile';
        backToTop.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: none;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;

        document.body.appendChild(backToTop);

        // 滚动显示/隐藏
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'flex';
                backToTop.style.alignItems = 'center';
                backToTop.style.justifyContent = 'center';
            } else {
                backToTop.style.display = 'none';
            }
        });

        // 点击返回顶部
        backToTop.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    /**
     * 优化导航栏收缩
     */
    optimizeNavbarCollapse() {
        const navbarToggler = document.querySelector('.navbar-toggler');
        const navbarCollapse = document.querySelector('.navbar-collapse');

        if (navbarToggler && navbarCollapse) {
            // 点击菜单项后自动收缩
            const navLinks = navbarCollapse.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (navbarCollapse.classList.contains('show')) {
                        navbarToggler.click();
                    }
                });
            });
        }
    }

    /**
     * 初始化移动端表格优化
     */
    initMobileTableOptimizations() {
        const tables = document.querySelectorAll('.table-responsive .table');

        tables.forEach(table => {
            this.createMobileTableView(table);
        });
    }

    /**
     * 创建移动端表格视图
     */
    createMobileTableView(table) {
        const container = table.closest('.table-responsive');
        if (!container) return;

        // 创建移动端卡片容器
        const mobileContainer = document.createElement('div');
        mobileContainer.className = 'table-mobile-cards mobile-only';
        mobileContainer.style.display = 'none';

        // 获取表头
        const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());

        // 转换表格行为卡片
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            const card = this.createMobileCard(row, headers);
            mobileContainer.appendChild(card);
        });

        container.appendChild(mobileContainer);

        // 添加视图切换按钮
        this.addTableViewToggle(container, table, mobileContainer);
    }

    /**
     * 创建移动端卡片
     */
    createMobileCard(row, headers) {
        const card = document.createElement('div');
        card.className = 'mobile-card';

        const cells = row.querySelectorAll('td');

        // 卡片头部
        const header = document.createElement('div');
        header.className = 'mobile-card-header';

        const title = document.createElement('div');
        title.className = 'mobile-card-title';
        title.textContent = cells[0]?.textContent.trim() || '项目';

        header.appendChild(title);
        card.appendChild(header);

        // 卡片内容
        const body = document.createElement('div');
        body.className = 'mobile-card-body';

        cells.forEach((cell, index) => {
            if (index === 0) return; // 跳过第一列（已用作标题）

            const field = document.createElement('div');
            field.className = 'mobile-field';

            const label = document.createElement('div');
            label.className = 'mobile-field-label';
            label.textContent = headers[index] || `字段${index}`;

            const value = document.createElement('div');
            value.className = 'mobile-field-value';
            value.innerHTML = cell.innerHTML;

            field.appendChild(label);
            field.appendChild(value);
            body.appendChild(field);
        });

        card.appendChild(body);

        // 操作按钮
        const actionCell = row.querySelector('.action-column');
        if (actionCell) {
            const actions = document.createElement('div');
            actions.className = 'mobile-card-actions';
            actions.innerHTML = actionCell.innerHTML;
            card.appendChild(actions);
        }

        return card;
    }

    /**
     * 添加表格视图切换按钮
     */
    addTableViewToggle(container, table, mobileContainer) {
        const toggle = document.createElement('button');
        toggle.className = 'btn btn-sm btn-outline-secondary mobile-table-toggle mobile-only';
        toggle.innerHTML = '<i class="fas fa-th-list"></i> 切换视图';
        toggle.style.marginBottom = '10px';

        let isCardView = false;

        toggle.addEventListener('click', function() {
            isCardView = !isCardView;

            if (isCardView) {
                table.style.display = 'none';
                mobileContainer.style.display = 'block';
                toggle.innerHTML = '<i class="fas fa-table"></i> 表格视图';
            } else {
                table.style.display = 'table';
                mobileContainer.style.display = 'none';
                toggle.innerHTML = '<i class="fas fa-th-list"></i> 卡片视图';
            }
        });

        container.insertBefore(toggle, container.firstChild);
    }

    /**
     * 初始化移动端表单增强
     */
    initMobileFormEnhancements() {
        // 优化输入框焦点
        this.optimizeInputFocus();

        // 添加表单验证增强
        this.enhanceFormValidation();

        // 优化选择器
        this.optimizeSelectors();

        // 添加输入辅助
        this.addInputAssistance();
    }

    /**
     * 优化输入框焦点
     */
    optimizeInputFocus() {
        const inputs = document.querySelectorAll('input, textarea, select');

        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                // 滚动到输入框
                setTimeout(() => {
                    this.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }, 300);

                // 添加焦点样式
                this.closest('.form-group')?.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                this.closest('.form-group')?.classList.remove('focused');
            });
        });
    }

    /**
     * 增强表单验证
     */
    enhanceFormValidation() {
        const forms = document.querySelectorAll('form');

        forms.forEach(form => {
            const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');

            inputs.forEach(input => {
                input.addEventListener('invalid', function(e) {
                    e.preventDefault();

                    // 滚动到第一个无效字段
                    this.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });

                    // 显示友好的错误消息
                    this.focus();

                    // 添加错误样式
                    this.classList.add('is-invalid');
                });

                input.addEventListener('input', function() {
                    if (this.validity.valid) {
                        this.classList.remove('is-invalid');
                        this.classList.add('is-valid');
                    }
                });
            });
        });
    }

    /**
     * 优化选择器
     */
    optimizeSelectors() {
        const selects = document.querySelectorAll('select');

        selects.forEach(select => {
            // 为长列表添加搜索功能
            if (select.options.length > 10) {
                this.addSelectSearch(select);
            }
        });
    }

    /**
     * 为选择框添加搜索功能
     */
    addSelectSearch(select) {
        const wrapper = document.createElement('div');
        wrapper.className = 'mobile-select-wrapper';

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'form-control mobile-select-search';
        searchInput.placeholder = '搜索选项...';
        searchInput.style.marginBottom = '8px';

        select.parentNode.insertBefore(wrapper, select);
        wrapper.appendChild(searchInput);
        wrapper.appendChild(select);

        const options = Array.from(select.options);

        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            options.forEach(option => {
                const text = option.textContent.toLowerCase();
                option.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });
    }

    /**
     * 添加输入辅助
     */
    addInputAssistance() {
        // 数字输入框添加步进按钮
        const numberInputs = document.querySelectorAll('input[type="number"]');

        numberInputs.forEach(input => {
            this.addNumberStepper(input);
        });
    }

    /**
     * 为数字输入框添加步进按钮
     */
    addNumberStepper(input) {
        const wrapper = document.createElement('div');
        wrapper.className = 'mobile-number-stepper';

        const decreaseBtn = document.createElement('button');
        decreaseBtn.type = 'button';
        decreaseBtn.className = 'btn btn-outline-secondary btn-sm';
        decreaseBtn.innerHTML = '<i class="fas fa-minus"></i>';

        const increaseBtn = document.createElement('button');
        increaseBtn.type = 'button';
        increaseBtn.className = 'btn btn-outline-secondary btn-sm';
        increaseBtn.innerHTML = '<i class="fas fa-plus"></i>';

        input.parentNode.insertBefore(wrapper, input);
        wrapper.appendChild(decreaseBtn);
        wrapper.appendChild(input);
        wrapper.appendChild(increaseBtn);

        decreaseBtn.addEventListener('click', function() {
            const step = parseFloat(input.step) || 1;
            const min = parseFloat(input.min);
            const current = parseFloat(input.value) || 0;
            const newValue = current - step;

            if (isNaN(min) || newValue >= min) {
                input.value = newValue;
                input.dispatchEvent(new Event('input'));
            }
        });

        increaseBtn.addEventListener('click', function() {
            const step = parseFloat(input.step) || 1;
            const max = parseFloat(input.max);
            const current = parseFloat(input.value) || 0;
            const newValue = current + step;

            if (isNaN(max) || newValue <= max) {
                input.value = newValue;
                input.dispatchEvent(new Event('input'));
            }
        });
    }

    /**
     * 初始化移动端模态框优化
     */
    initMobileModalOptimizations() {
        const modals = document.querySelectorAll('.modal');

        modals.forEach(modal => {
            this.optimizeModal(modal);
        });
    }

    /**
     * 优化模态框
     */
    optimizeModal(modal) {
        const dialog = modal.querySelector('.modal-dialog');
        const body = modal.querySelector('.modal-body');

        if (dialog) {
            // 添加移动端样式类
            dialog.classList.add('mobile-modal');

            // 优化模态框高度
            if (body) {
                const maxHeight = window.innerHeight * 0.8;
                body.style.maxHeight = maxHeight + 'px';
                body.style.overflowY = 'auto';
            }
        }

        // 添加手势关闭
        this.addModalSwipeClose(modal);
    }

    /**
     * 添加模态框滑动关闭
     */
    addModalSwipeClose(modal) {
        let startY = 0;
        let currentY = 0;
        let isDragging = false;

        const dialog = modal.querySelector('.modal-dialog');

        modal.addEventListener('touchstart', function(e) {
            startY = e.touches[0].clientY;
            isDragging = true;
        }, { passive: true });

        modal.addEventListener('touchmove', function(e) {
            if (!isDragging) return;

            currentY = e.touches[0].clientY;
            const deltaY = currentY - startY;

            if (deltaY > 0) {
                dialog.style.transform = `translateY(${deltaY}px)`;
                dialog.style.opacity = 1 - (deltaY / 300);
            }
        }, { passive: true });

        modal.addEventListener('touchend', function(e) {
            if (!isDragging) return;

            const deltaY = currentY - startY;

            if (deltaY > 100) {
                // 关闭模态框
                $(modal).modal('hide');
            } else {
                // 恢复位置
                dialog.style.transform = '';
                dialog.style.opacity = '';
            }

            isDragging = false;
        }, { passive: true });
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 窗口大小变化时重新初始化
        window.addEventListener('resize', () => {
            const wasMobile = this.isMobile;
            this.isMobile = window.innerWidth <= 768;

            if (wasMobile !== this.isMobile) {
                if (this.isMobile) {
                    this.init();
                } else {
                    this.cleanup();
                }
            }
        });

        // 设备方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.optimizeTouchTargets();
            }, 100);
        });
    }

    /**
     * 清理移动端优化
     */
    cleanup() {
        document.body.classList.remove('mobile-device');

        const backToTop = document.querySelector('.back-to-top-mobile');
        if (backToTop) {
            backToTop.remove();
        }
    }
}

// 初始化移动端增强功能
document.addEventListener('DOMContentLoaded', function() {
    new MobileEnhancements();
});

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileEnhancements;
}
